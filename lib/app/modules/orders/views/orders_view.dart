import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/extension.dart';

import '../../../../colors.dart';
import '../../../widgets/sheet_wrapper.dart';
import '../../order_detail/bindings/order_detail_binding.dart';
import '../../order_detail/views/order_detail_view.dart';
import '../controllers/orders_controller.dart';

class OrdersView extends GetView<OrdersController> {
  OrdersView({super.key});

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    // Add scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        controller.onEndScroll();
      }
    });
    controller.talker.debug('OrdersView build');

    return Scaffold(
      body: controller.obx(
        (state) => _buildContent(context),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Text(error.toString()),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showOrderDetailSheet();
        },
        backgroundColor: ErpColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [
        _buildHeader(context),
        Expanded(
          child: _buildTransactionList(context),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ErpColors.gradientStart,
            ErpColors.gradientEnd,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildAppBar(context),
              const SizedBox(height: 16),
              _buildSearchBar(context),
              const SizedBox(height: 16),
              _buildFilterButtons(context),
              const SizedBox(height: 16),
              _buildStatistics(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(
            Icons.arrow_back,
            color: ErpColors.textWhite,
            size: 24,
          ),
        ),
        Expanded(
          child: Text(
            'transactions_title'.tr,
            style: const TextStyle(
              color: ErpColors.textWhite,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          onPressed: () {
            _showOrderDetailSheet();
          },
          icon: const Icon(
            Icons.add,
            color: ErpColors.textWhite,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ErpColors.overlayMedium,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        onChanged: controller.searchOrders,
        style: const TextStyle(color: ErpColors.textWhite),
        decoration: InputDecoration(
          hintText: 'transactions_search'.tr,
          hintStyle: const TextStyle(color: ErpColors.overlayStrong),
          prefixIcon: const Icon(
            Icons.search,
            color: ErpColors.overlayStrong,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildFilterButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildFilterButton(
            icon: Icons.calendar_today,
            label: 'transactions_date'.tr,
            onTap: () {
              // TODO: Show date filter
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildFilterButton(
            icon: Icons.category,
            label: 'transactions_category'.tr,
            onTap: () {
              // TODO: Show category filter
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildFilterButton(
            icon: Icons.filter_list,
            label: 'transactions_filter'.tr,
            onTap: () {
              // TODO: Show type filter
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFilterButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: ErpColors.overlayMedium,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: ErpColors.textWhite,
              size: 16,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                label,
                style: const TextStyle(
                  color: ErpColors.textWhite,
                  fontSize: 12,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics(BuildContext context) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ErpColors.overlayLight,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Text(
                    'transactions_income'.tr,
                    style: const TextStyle(
                      color: ErpColors.overlayStrong,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '+${controller.totalIncome.value.formattedAmount}',
                    style: const TextStyle(
                      color: ErpColors.incomeLight,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  Text(
                    'transactions_expense'.tr,
                    style: const TextStyle(
                      color: ErpColors.overlayStrong,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '-${controller.totalExpense.value.formattedAmount}',
                    style: const TextStyle(
                      color: ErpColors.expenseLight,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildTransactionList(BuildContext context) {
    return Obx(() {
      final groupedOrders = controller.groupedOrders;

      if (groupedOrders.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.receipt_long,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'transactions_no_data'.tr,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: groupedOrders.length + (controller.hasMoreData.value ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == groupedOrders.length) {
              // Loading indicator for pagination
              return Obx(() {
                return controller.isLoadingMore.value
                    ? const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    : const SizedBox.shrink();
              });
            }

            final dateKey = groupedOrders.keys.elementAt(index);
            final orders = groupedOrders[dateKey]!;

            return _buildDateGroup(context, dateKey, orders);
          },
        ),
      );
    });
  }

  Widget _buildDateGroup(BuildContext context, String dateKey, List<dynamic> orders) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            dateKey,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: ErpColors.textPrimary,
            ),
          ),
        ),
        ...orders.map((order) => _buildTransactionItem(context, order)),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildTransactionItem(BuildContext context, dynamic order) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ErpColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ErpColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildTransactionIcon(order),
          const SizedBox(width: 12),
          Expanded(
            child: _buildTransactionInfo(order),
          ),
          _buildTransactionAmount(order),
        ],
      ),
    );
  }

  Widget _buildTransactionIcon(dynamic order) {
    // TODO: Get category icon and color from order.parent
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: ErpColors.iconBackgroundBlue,
        borderRadius: BorderRadius.circular(24),
      ),
      child: const Icon(
        Icons.receipt,
        color: ErpColors.primary,
        size: 24,
      ),
    );
  }

  Widget _buildTransactionInfo(dynamic order) {
    final triggerAt = order.triggerAt ?? DateTime.now();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          order.note ?? 'transactions_no_data'.tr,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: ErpColors.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          '${triggerAt.formattedTime} • ${order.parent.target?.name ?? 'transactions_category'.tr}',
          style: const TextStyle(
            fontSize: 12,
            color: ErpColors.textSecondary,
          ),
        ),
        if (order.note != null && order.note!.length > 20) ...[
          const SizedBox(height: 2),
          Text(
            order.note!,
            style: const TextStyle(
              fontSize: 11,
              color: ErpColors.textHint,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildTransactionAmount(dynamic order) {
    final isIncome = (order.type ?? 0) == 2;
    final color = isIncome ? ErpColors.income : ErpColors.expense;

    return Text(
      order.formattedAmountWithSign,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: color,
      ),
    );
  }

  // 顯示新增交易 Sheet
  Future<void> _showOrderDetailSheet() async {
    await SheetWrapper(
      title: '新增交易',
      onInit: () {
        // 初始化 OrderDetailBinding
        final binding = OrderDetailBinding();
        binding.dependencies();
      },
      child: const OrderDetailView(),
    ).sheet(
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
      ignoreSafeArea: true,
    );
  }
}
